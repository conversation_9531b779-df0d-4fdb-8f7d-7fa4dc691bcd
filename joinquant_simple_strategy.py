# 聚宽简化版策略：大盘择时+价值选股
# 适用于聚宽平台的简化版本

def initialize(context):
    """初始化函数"""
    # 策略参数
    g.market_pb_buy = 2.0      # 大盘PB买入阈值
    g.market_pb_full = 1.2     # 大盘PB满仓阈值
    g.max_stocks = 20          # 最大持股数量
    g.sell_half_return = 1.0   # 减半卖出收益率
    g.sell_all_return = 2.0    # 清仓收益率
    
    # 设置基准
    set_benchmark('000300.XSHG')
    
    # 设置交易成本
    set_order_cost(OrderCost(
        close_tax=0.001,
        open_commission=0.0003,
        close_commission=0.0003,
        min_commission=5
    ), type='stock')
    
    # 持仓成本记录
    g.position_cost = {}
    
    # 每日运行
    run_daily(trade_main, time='09:30')
    
    log.info("策略初始化完成")

def get_market_pb():
    """获取大盘市净率"""
    # 使用沪深300成分股计算市场PB
    stocks = get_index_stocks('000300.XSHG')
    
    q = query(
        valuation.code,
        valuation.market_cap,
        valuation.pb_ratio
    ).filter(
        valuation.code.in_(stocks),
        valuation.pb_ratio > 0,
        valuation.pb_ratio < 10  # 过滤异常值
    )
    
    df = get_fundamentals(q)
    
    if len(df) == 0:
        return 2.0
    
    # 市值加权平均PB
    total_cap = df['market_cap'].sum()
    weighted_pb = (df['market_cap'] * df['pb_ratio']).sum() / total_cap
    
    return weighted_pb

def get_target_position():
    """根据大盘PB确定目标仓位"""
    market_pb = get_market_pb()
    
    if market_pb >= g.market_pb_buy:
        return 0.0, market_pb
    elif market_pb <= g.market_pb_full:
        return 1.0, market_pb
    else:
        # 线性插值
        ratio = (g.market_pb_buy - market_pb) / (g.market_pb_buy - g.market_pb_full)
        return max(0, min(1, ratio)), market_pb

def get_stock_pool():
    """获取股票池"""
    # 获取全部A股
    stocks = list(get_all_securities(['stock']).index)
    
    # 过滤掉ST、停牌等
    current_data = get_current_data()
    
    valid_stocks = []
    for stock in stocks:
        data = current_data[stock]
        if (not data.paused and 
            not data.is_st and 
            'ST' not in data.name and
            '*' not in data.name):
            valid_stocks.append(stock)
    
    return valid_stocks

def calculate_scores(context, stocks):
    """计算股票评分"""
    if len(stocks) == 0:
        return []

    # 获取基本面数据
    q = query(
        valuation.code,
        valuation.pe_ratio,
        valuation.pb_ratio,
        valuation.market_cap
    ).filter(
        valuation.code.in_(stocks),
        valuation.pe_ratio > 0,
        valuation.pe_ratio < 100,
        valuation.pb_ratio > 0,
        valuation.pb_ratio < 10,
        valuation.market_cap > 50  # 市值大于50亿
    )

    df = get_fundamentals(q)

    if len(df) == 0:
        return []

    # 获取价格数据
    prices = get_price(df['code'].tolist(), count=250, end_date=context.current_dt, fields=['close'])
    
    scores = []
    for _, row in df.iterrows():
        stock = row['code']
        
        try:
            # 计算从年内最低点的涨幅
            stock_prices = prices['close'][stock].dropna()
            if len(stock_prices) < 50:  # 至少50个交易日数据
                continue
                
            current_price = stock_prices.iloc[-1]
            min_price = stock_prices.min()
            return_from_low = (current_price / min_price - 1) if min_price > 0 else 0
            
            # PE评分 (越低越好)
            pe_score = max(0, 100 - (row['pe_ratio'] - 5) * 5)
            
            # PB评分 (越低越好)
            pb_score = max(0, 100 - (row['pb_ratio'] - 0.5) * 50)
            
            # 涨幅评分 (涨幅越小越好)
            return_score = max(0, 100 - return_from_low * 100)
            
            # 总分
            total_score = pe_score + pb_score + return_score
            
            scores.append({
                'stock': stock,
                'pe': row['pe_ratio'],
                'pb': row['pb_ratio'],
                'return_from_low': return_from_low,
                'score': total_score
            })
            
        except:
            continue
    
    # 按评分排序
    scores.sort(key=lambda x: x['score'], reverse=True)
    return scores

def check_sell_signals(context):
    """检查卖出信号"""
    sells = []
    current_data = get_current_data()

    for stock in context.portfolio.positions:
        position = context.portfolio.positions[stock]
        if position.total_amount == 0:
            continue

        # 获取成本价
        cost = g.position_cost.get(stock, position.avg_cost)
        current_price = current_data[stock].last_price

        if current_price > 0 and cost > 0:
            return_rate = current_price / cost - 1

            if return_rate >= g.sell_all_return:
                sells.append({'stock': stock, 'action': 'all', 'return': return_rate})
            elif return_rate >= g.sell_half_return:
                sells.append({'stock': stock, 'action': 'half', 'return': return_rate})

    return sells

def trade_main(context):
    """主交易函数"""
    # 1. 获取目标仓位
    target_pos, market_pb = get_target_position()
    
    log.info(f"大盘PB: {market_pb:.2f}, 目标仓位: {target_pos:.1%}")
    
    # 2. 检查卖出信号
    sells = check_sell_signals(context)
    
    # 执行卖出
    for sell in sells:
        stock = sell['stock']
        if sell['action'] == 'all':
            order_target_percent(stock, 0)
            if stock in g.position_cost:
                del g.position_cost[stock]
            log.info(f"清仓 {stock}, 收益: {sell['return']:.1%}")
        elif sell['action'] == 'half':
            current_percent = context.portfolio.positions[stock].value / context.portfolio.total_value
            order_target_percent(stock, current_percent / 2)
            log.info(f"减半 {stock}, 收益: {sell['return']:.1%}")
    
    # 3. 如果目标仓位为0，清仓
    if target_pos <= 0:
        for stock in context.portfolio.positions:
            order_target_percent(stock, 0)
        g.position_cost.clear()
        log.info("大盘PB过高，清仓")
        return
    
    # 4. 选股和买入
    stock_pool = get_stock_pool()
    scores = calculate_scores(stock_pool[:500])  # 限制计算范围提高速度
    
    if len(scores) == 0:
        log.info("没有符合条件的股票")
        return
    
    # 选择前N只股票
    selected = scores[:g.max_stocks]
    
    # 计算每只股票的权重
    weight_per_stock = target_pos / len(selected)
    
    # 执行买入
    buy_count = 0
    for stock_info in selected:
        stock = stock_info['stock']
        
        # 如果已经持有，跳过
        if context.portfolio.positions[stock].total_amount > 0:
            continue
        
        # 买入
        order_target_percent(stock, weight_per_stock)
        
        # 记录成本
        current_price = get_current_data()[stock].last_price
        g.position_cost[stock] = current_price
        
        buy_count += 1
        
        if buy_count >= 5:  # 每次最多买入5只，避免冲击
            break
    
    log.info(f"选股完成，买入 {buy_count} 只股票")

def after_market_close(context):
    """收盘后运行"""
    total_value = context.portfolio.total_value
    positions = len([p for p in context.portfolio.positions.values() if p.total_amount > 0])
    
    log.info(f"收盘 - 总资产: {total_value:.0f}, 持仓: {positions}只")

# 使用说明：
# 1. 复制这段代码到聚宽平台
# 2. 设置回测时间：2015-01-01 到现在
# 3. 初始资金：100万
# 4. 点击运行回测
# 
# 策略特点：
# - 大盘择时：PB高时减仓，PB低时加仓
# - 价值选股：选择PE、PB低且涨幅小的股票
# - 止盈策略：涨100%减半，涨200%清仓
# - 风险控制：最多持有20只股票，分散风险
